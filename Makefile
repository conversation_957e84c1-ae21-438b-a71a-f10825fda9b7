# =============================================================================
# CMDB Project Makefile
# =============================================================================
#
# 这个 Makefile 提供了构建、测试、部署 CMDB 项目的标准化命令
# 支持跨平台构建、版本管理、依赖检查等功能
#
# 快速开始:
#   make help     - 显示所有可用命令
#   make build    - 构建项目
#   make test     - 运行测试
#   make clean    - 清理构建产物
#
# =============================================================================

# -----------------------------------------------------------------------------
# 项目配置
# -----------------------------------------------------------------------------

# 项目基本信息
PROJECT_NAME := cmdb
BINARY_NAME := cmdb
MAIN_PACKAGE := .
MODULE_NAME := cmdb

# 版本信息 (可通过环境变量覆盖)
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
GIT_COMMIT ?= $(shell git rev-parse HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH ?= $(shell git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
BUILD_TIME ?= $(shell date -u +%Y-%m-%dT%H:%M:%SZ)
BUILD_USER ?= $(shell whoami 2>/dev/null || echo "unknown")
BUILD_HOST ?= $(shell hostname 2>/dev/null || echo "unknown")

# Go 相关配置
GO := go
GOFMT := gofmt
GOLINT := golangci-lint
GOTEST := $(GO) test
GOBUILD := $(GO) build
GOCLEAN := $(GO) clean
GOMOD := $(GO) mod
GOGET := $(GO) get

# 构建配置
CGO_ENABLED ?= 0
GOOS ?= $(shell $(GO) env GOOS)
GOARCH ?= $(shell $(GO) env GOARCH)
GO_VERSION := $(shell $(GO) version | awk '{print $$3}')

# 构建标志
LDFLAGS := -s -w
LDFLAGS += -X $(MODULE_NAME)/version.Version=$(VERSION)
LDFLAGS += -X $(MODULE_NAME)/version.GitCommit=$(GIT_COMMIT)
LDFLAGS += -X $(MODULE_NAME)/version.GitBranch=$(GIT_BRANCH)
LDFLAGS += -X $(MODULE_NAME)/version.BuildTime=$(BUILD_TIME)
LDFLAGS += -X $(MODULE_NAME)/version.BuildUser=$(BUILD_USER)
LDFLAGS += -X $(MODULE_NAME)/version.BuildHost=$(BUILD_HOST)
LDFLAGS += -X $(MODULE_NAME)/version.GoVersion=$(GO_VERSION)

BUILD_FLAGS := -v -ldflags "$(LDFLAGS)"
TEST_FLAGS := -v -race -coverprofile=coverage.out

# 目录配置
BUILD_DIR := build
DIST_DIR := dist
DOCS_DIR := docs
SCRIPTS_DIR := scripts

# 跨平台构建目标
PLATFORMS := linux/amd64 linux/arm64 darwin/amd64 darwin/arm64 windows/amd64

# 颜色定义 (用于美化输出)
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
MAGENTA := \033[35m
CYAN := \033[36m
WHITE := \033[37m
RESET := \033[0m

# -----------------------------------------------------------------------------
# 默认目标
# -----------------------------------------------------------------------------

.DEFAULT_GOAL := help

# 所有 .PHONY 目标声明
.PHONY: all build clean test run install uninstall
.PHONY: build-all build-linux build-darwin build-windows
.PHONY: test-unit test-integration test-coverage test-bench
.PHONY: lint fmt vet check deps deps-update deps-verify
.PHONY: docker docker-build docker-run docker-push
.PHONY: release release-snapshot
.PHONY: docs docs-serve docs-build
.PHONY: dev watch
.PHONY: info version env
.PHONY: help

# -----------------------------------------------------------------------------
# 主要构建目标
# -----------------------------------------------------------------------------

## 构建所有目标 (默认)
all: clean deps check build test

## 构建项目
build:
	@printf "$(CYAN)🔨 构建 $(PROJECT_NAME)...$(RESET)\n"
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
		$(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PACKAGE)
	@printf "$(GREEN)✅ 构建完成: $(BUILD_DIR)/$(BINARY_NAME)$(RESET)\n"
	@ls -lh $(BUILD_DIR)/$(BINARY_NAME)

## 清理构建产物
clean:
	@printf "$(YELLOW)🧹 清理构建产物...$(RESET)\n"
	@rm -rf $(BUILD_DIR) $(DIST_DIR)
	@rm -f coverage.out coverage.html
	@rm -f $(BINARY_NAME) $(BINARY_NAME).exe
	@$(GOCLEAN) -cache -testcache -modcache 2>/dev/null || true
	@printf "$(GREEN)✅ 清理完成$(RESET)\n"

## 运行项目 (先构建)
run: build
	@printf "$(BLUE)🚀 运行 $(PROJECT_NAME)...$(RESET)\n"
	@$(BUILD_DIR)/$(BINARY_NAME)

## 安装到系统路径
install: build
	@printf "$(CYAN)📦 安装 $(PROJECT_NAME)...$(RESET)\n"
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@printf "$(GREEN)✅ 安装完成: /usr/local/bin/$(BINARY_NAME)$(RESET)\n"

## 从系统路径卸载
uninstall:
	@printf "$(YELLOW)🗑️  卸载 $(PROJECT_NAME)...$(RESET)\n"
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@printf "$(GREEN)✅ 卸载完成$(RESET)\n"

# -----------------------------------------------------------------------------
# 跨平台构建目标
# -----------------------------------------------------------------------------

## 构建所有平台版本
build-all: clean
	@printf "$(CYAN)🔨 构建所有平台版本...$(RESET)\n"
	@mkdir -p $(DIST_DIR)
	@for platform in $(PLATFORMS); do \
		GOOS=$$(echo $$platform | cut -d'/' -f1); \
		GOARCH=$$(echo $$platform | cut -d'/' -f2); \
		output_name=$(BINARY_NAME)-$(VERSION)-$$GOOS-$$GOARCH; \
		if [ $$GOOS = "windows" ]; then output_name=$$output_name.exe; fi; \
		printf "$(BLUE)  构建 $$GOOS/$$GOARCH...$(RESET)\n"; \
		CGO_ENABLED=0 GOOS=$$GOOS GOARCH=$$GOARCH \
			$(GOBUILD) $(BUILD_FLAGS) -o $(DIST_DIR)/$$output_name $(MAIN_PACKAGE); \
		if [ $$? -eq 0 ]; then \
			printf "$(GREEN)    ✅ $$output_name$(RESET)\n"; \
		else \
			printf "$(RED)    ❌ $$output_name$(RESET)\n"; \
		fi; \
	done
	@printf "$(GREEN)✅ 跨平台构建完成$(RESET)\n"
	@ls -lh $(DIST_DIR)/

## 构建 Linux 版本
build-linux:
	@$(MAKE) build GOOS=linux GOARCH=amd64

## 构建 macOS 版本
build-darwin:
	@$(MAKE) build GOOS=darwin GOARCH=amd64

## 构建 Windows 版本
build-windows:
	@$(MAKE) build GOOS=windows GOARCH=amd64

# -----------------------------------------------------------------------------
# 测试目标
# -----------------------------------------------------------------------------

## 运行所有测试
test: test-unit

## 运行单元测试
test-unit:
	@printf "$(CYAN)🧪 运行单元测试...$(RESET)\n"
	@CGO_ENABLED=1 $(GOTEST) $(TEST_FLAGS) ./...
	@printf "$(GREEN)✅ 单元测试完成$(RESET)\n"

## 运行集成测试
test-integration:
	@printf "$(CYAN)🔗 运行集成测试...$(RESET)\n"
	@CGO_ENABLED=1 $(GOTEST) -tags=integration $(TEST_FLAGS) ./...
	@printf "$(GREEN)✅ 集成测试完成$(RESET)\n"

## 生成测试覆盖率报告
test-coverage: test-unit
	@printf "$(CYAN)📊 生成覆盖率报告...$(RESET)\n"
	@$(GO) tool cover -html=coverage.out -o coverage.html
	@printf "$(GREEN)✅ 覆盖率报告: coverage.html$(RESET)\n"

## 运行性能测试
test-bench:
	@printf "$(CYAN)⚡ 运行性能测试...$(RESET)\n"
	@$(GOTEST) -bench=. -benchmem ./...
	@printf "$(GREEN)✅ 性能测试完成$(RESET)\n"

# -----------------------------------------------------------------------------
# 代码质量检查
# -----------------------------------------------------------------------------

## 运行所有检查
check: fmt vet lint

## 格式化代码
fmt:
	@printf "$(CYAN)🎨 格式化代码...$(RESET)\n"
	@$(GOFMT) -s -w .
	@$(GO) mod tidy
	@printf "$(GREEN)✅ 代码格式化完成$(RESET)\n"

## 运行 go vet
vet:
	@printf "$(CYAN)🔍 运行 go vet...$(RESET)\n"
	@$(GO) vet ./...
	@printf "$(GREEN)✅ go vet 检查完成$(RESET)\n"

## 运行 linter
lint:
	@printf "$(CYAN)🔍 运行 linter...$(RESET)\n"
	@if command -v $(GOLINT) >/dev/null 2>&1; then \
		$(GOLINT) run ./...; \
		printf "$(GREEN)✅ linter 检查完成$(RESET)\n"; \
	else \
		printf "$(YELLOW)⚠️  golangci-lint 未安装，跳过 linter 检查$(RESET)\n"; \
		printf "$(YELLOW)   安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest$(RESET)\n"; \
	fi

# -----------------------------------------------------------------------------
# 依赖管理
# -----------------------------------------------------------------------------

## 下载依赖
deps:
	@printf "$(CYAN)📦 下载依赖...$(RESET)\n"
	@$(GOMOD) download
	@$(GOMOD) tidy
	@printf "$(GREEN)✅ 依赖下载完成$(RESET)\n"

## 更新依赖
deps-update:
	@printf "$(CYAN)🔄 更新依赖...$(RESET)\n"
	@$(GOGET) -u ./...
	@$(GOMOD) tidy
	@printf "$(GREEN)✅ 依赖更新完成$(RESET)\n"

## 验证依赖
deps-verify:
	@printf "$(CYAN)🔐 验证依赖...$(RESET)\n"
	@$(GOMOD) verify
	@printf "$(GREEN)✅ 依赖验证完成$(RESET)\n"

# -----------------------------------------------------------------------------
# 开发工具
# -----------------------------------------------------------------------------

## 开发模式 (监视文件变化)
dev: watch

## 监视文件变化并自动重新构建
watch:
	@printf "$(CYAN)👀 启动文件监视模式...$(RESET)\n"
	@printf "$(YELLOW)按 Ctrl+C 停止监视$(RESET)\n"
	@if command -v gowatch >/dev/null 2>&1; then \
		CGO_ENABLED=$(CGO_ENABLED) gowatch; \
	elif command -v air >/dev/null 2>&1; then \
		air; \
	else \
		printf "$(RED)❌ 未找到 gowatch 或 air 工具$(RESET)\n"; \
		printf "$(YELLOW)安装命令:$(RESET)\n"; \
		printf "$(YELLOW)  go install github.com/silenceper/gowatch@latest$(RESET)\n"; \
		printf "$(YELLOW)  或$(RESET)\n"; \
		printf "$(YELLOW)  go install github.com/cosmtrek/air@latest$(RESET)\n"; \
		exit 1; \
	fi

# -----------------------------------------------------------------------------
# Docker 支持
# -----------------------------------------------------------------------------

## 构建 Docker 镜像
docker-build:
	@printf "$(CYAN)🐳 构建 Docker 镜像...$(RESET)\n"
	@docker build -t $(PROJECT_NAME):$(VERSION) .
	@docker tag $(PROJECT_NAME):$(VERSION) $(PROJECT_NAME):latest
	@printf "$(GREEN)✅ Docker 镜像构建完成$(RESET)\n"

## 运行 Docker 容器
docker-run: docker-build
	@printf "$(BLUE)🚀 运行 Docker 容器...$(RESET)\n"
	@docker run --rm -it -p 8080:8080 $(PROJECT_NAME):$(VERSION)

## 推送 Docker 镜像
docker-push: docker-build
	@printf "$(CYAN)📤 推送 Docker 镜像...$(RESET)\n"
	@docker push $(PROJECT_NAME):$(VERSION)
	@docker push $(PROJECT_NAME):latest
	@printf "$(GREEN)✅ Docker 镜像推送完成$(RESET)\n"

# -----------------------------------------------------------------------------
# 发布管理
# -----------------------------------------------------------------------------

## 创建发布版本
release: clean deps check test build-all
	@printf "$(MAGENTA)🎉 创建发布版本 $(VERSION)...$(RESET)\n"
	@mkdir -p $(DIST_DIR)/release
	@for file in $(DIST_DIR)/$(BINARY_NAME)-*; do \
		if [ -f "$$file" ]; then \
			cp "$$file" $(DIST_DIR)/release/; \
		fi; \
	done
	@printf "$(GREEN)✅ 发布版本创建完成: $(DIST_DIR)/release/$(RESET)\n"
	@ls -lh $(DIST_DIR)/release/

## 创建快照版本 (用于开发)
release-snapshot: clean build-all
	@printf "$(YELLOW)📸 创建快照版本...$(RESET)\n"
	@mkdir -p $(DIST_DIR)/snapshot
	@for file in $(DIST_DIR)/$(BINARY_NAME)-*; do \
		if [ -f "$$file" ]; then \
			cp "$$file" $(DIST_DIR)/snapshot/; \
		fi; \
	done
	@printf "$(GREEN)✅ 快照版本创建完成: $(DIST_DIR)/snapshot/$(RESET)\n"

# -----------------------------------------------------------------------------
# 文档管理
# -----------------------------------------------------------------------------

## 生成文档
docs-build:
	@printf "$(CYAN)📚 生成文档...$(RESET)\n"
	@mkdir -p $(DOCS_DIR)
	@$(GO) doc -all > $(DOCS_DIR)/api.txt
	@printf "$(GREEN)✅ 文档生成完成: $(DOCS_DIR)/$(RESET)\n"

## 启动文档服务器
docs-serve:
	@printf "$(BLUE)📖 启动文档服务器...$(RESET)\n"
	@printf "$(YELLOW)访问: http://localhost:6060/pkg/$(MODULE_NAME)/$(RESET)\n"
	@$(GO) doc -http=:6060

# -----------------------------------------------------------------------------
# 信息查看
# -----------------------------------------------------------------------------

## 显示项目信息
info:
	@printf "$(CYAN)📋 项目信息$(RESET)\n"
	@printf "$(WHITE)项目名称:$(RESET) $(PROJECT_NAME)\n"
	@printf "$(WHITE)版本:$(RESET)     $(VERSION)\n"
	@printf "$(WHITE)Git提交:$(RESET)  $(GIT_COMMIT)\n"
	@printf "$(WHITE)Git分支:$(RESET)  $(GIT_BRANCH)\n"
	@printf "$(WHITE)构建时间:$(RESET) $(BUILD_TIME)\n"
	@printf "$(WHITE)构建用户:$(RESET) $(BUILD_USER)\n"
	@printf "$(WHITE)构建主机:$(RESET) $(BUILD_HOST)\n"
	@printf "$(WHITE)Go版本:$(RESET)   $(GO_VERSION)\n"

## 显示版本信息
version:
	@printf "$(PROJECT_NAME) $(VERSION)\n"

## 显示环境信息
env:
	@printf "$(CYAN)🌍 环境信息$(RESET)\n"
	@printf "$(WHITE)GOOS:$(RESET)        $(GOOS)\n"
	@printf "$(WHITE)GOARCH:$(RESET)      $(GOARCH)\n"
	@printf "$(WHITE)CGO_ENABLED:$(RESET) $(CGO_ENABLED)\n"
	@printf "$(WHITE)Go版本:$(RESET)      $(GO_VERSION)\n"
	@printf "$(WHITE)Go路径:$(RESET)      $(shell $(GO) env GOPATH)\n"
	@printf "$(WHITE)Go根目录:$(RESET)    $(shell $(GO) env GOROOT)\n"

# -----------------------------------------------------------------------------
# 帮助信息
# -----------------------------------------------------------------------------

## 显示帮助信息
help:
	@printf "$(MAGENTA)╔══════════════════════════════════════════════════════════════╗$(RESET)\n"
	@printf "$(MAGENTA)║                    CMDB 项目 Makefile                        ║$(RESET)\n"
	@printf "$(MAGENTA)╚══════════════════════════════════════════════════════════════╝$(RESET)\n"
	@printf "\n"
	@printf "$(CYAN)📋 主要命令:$(RESET)\n"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)
	@printf "\n"
	@printf "$(CYAN)🔧 常用组合:$(RESET)\n"
	@printf "  $(YELLOW)make all$(RESET)              完整构建流程 (清理+依赖+检查+构建+测试)\n"
	@printf "  $(YELLOW)make dev$(RESET)              开发模式 (文件监视)\n"
	@printf "  $(YELLOW)make release$(RESET)          创建发布版本\n"
	@printf "  $(YELLOW)make docker-run$(RESET)       构建并运行 Docker 容器\n"
	@printf "\n"
	@printf "$(CYAN)📚 更多信息:$(RESET)\n"
	@printf "  $(WHITE)make info$(RESET)             显示项目信息\n"
	@printf "  $(WHITE)make env$(RESET)              显示环境信息\n"
	@printf "  $(WHITE)make version$(RESET)          显示版本信息\n"
	@printf "\n"
	@printf "$(CYAN)🌐 环境变量:$(RESET)\n"
	@printf "  $(WHITE)VERSION$(RESET)               指定版本号\n"
	@printf "  $(WHITE)GOOS$(RESET)                  目标操作系统\n"
	@printf "  $(WHITE)GOARCH$(RESET)                目标架构\n"
	@printf "  $(WHITE)CGO_ENABLED$(RESET)           是否启用 CGO (默认: 0)\n"
	@printf "\n"
	@printf "$(CYAN)💡 示例:$(RESET)\n"
	@printf "  $(WHITE)make build GOOS=linux GOARCH=amd64$(RESET)\n"
	@printf "  $(WHITE)make release VERSION=1.0.0$(RESET)\n"
	@printf "  $(WHITE)make test-coverage$(RESET)\n"
	@printf "\n"
